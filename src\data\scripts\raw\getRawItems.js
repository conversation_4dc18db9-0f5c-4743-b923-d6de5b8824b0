// node src\data\scripts\raw\getRawItems.js

import fs from "node:fs";
import path from "node:path";
import dame from "dame";

const filename = "rawItems.json";
const metadataFilename = "rawItemsMetadata.json";
const url =
	"https://raw.githubusercontent.com/ao-data/ao-bin-dumps/refs/heads/master/formatted/items.json";

const filepath = path.join("./src/data/scripts/raw", filename);
const metadataFilepath = path.join("./src/data/scripts/raw", metadataFilename);

const { response, isError } = await dame.get(url);

if (isError) {
	console.error("Error fetching data:", response);
	process.exit(1);
}

/** @type {Buffer} */
const buffer = response;

const text = buffer.toString("utf8");
const json = JSON.parse(text);

fs.writeFileSync(filepath, JSON.stringify(json));

const metadata = {
	date: new Date().toISOString(),
	url,
};

fs.writeFileSync(metadataFilepath, JSON.stringify(metadata, null, 4));
